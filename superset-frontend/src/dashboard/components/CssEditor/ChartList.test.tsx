/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { ThemeProvider } from '@superset-ui/core';
import { theme } from 'src/preamble';
import ChartList from './ChartList';
import { CHART_TYPE } from 'src/dashboard/util/componentTypes';

const mockStore = configureStore({
  reducer: {
    dashboardLayout: (state = { present: {} }) => state,
    sliceEntities: (state = { slices: {} }) => state,
  },
});

const mockStoreWithData = configureStore({
  reducer: {
    dashboardLayout: (
      state = {
        present: {
          'CHART_1': {
            type: CHART_TYPE,
            id: 'CHART_1',
            meta: {
              chartId: 1,
              sliceNameOverride: 'Test Chart Override',
              sliceNameOverrideRU: 'Тестовый график',
            },
          },
          'CHART_2': {
            type: CHART_TYPE,
            id: 'CHART_2',
            meta: {
              chartId: 2,
            },
          },
        },
      },
    ) => state,
    sliceEntities: (
      state = {
        slices: {
          1: {
            slice_id: 1,
            slice_name: 'Original Chart Name',
          },
          2: {
            slice_id: 2,
            slice_name: 'Another Chart',
          },
        },
      },
    ) => state,
  },
});

const renderWithProvider = (store = mockStore) => {
  return render(
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <ChartList />
      </ThemeProvider>
    </Provider>,
  );
};

describe('ChartList', () => {
  it('renders search input', () => {
    renderWithProvider();
    expect(screen.getByPlaceholderText('Search charts by name or ID...')).toBeInTheDocument();
  });

  it('renders table with chart data', () => {
    renderWithProvider(mockStoreWithData);
    
    // Check if table headers are present
    expect(screen.getByText('Slice ID')).toBeInTheDocument();
    expect(screen.getByText('Display Name')).toBeInTheDocument();
    expect(screen.getByText('Original Name')).toBeInTheDocument();
    expect(screen.getByText('Override (EN)')).toBeInTheDocument();
    expect(screen.getByText('Override (RU)')).toBeInTheDocument();
    
    // Check if chart data is displayed
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('Test Chart Override')).toBeInTheDocument();
    expect(screen.getByText('Original Chart Name')).toBeInTheDocument();
    expect(screen.getByText('Another Chart')).toBeInTheDocument();
  });

  it('renders empty state when no charts', () => {
    renderWithProvider();
    
    // Should still render the search input and table headers
    expect(screen.getByPlaceholderText('Search charts by name or ID...')).toBeInTheDocument();
    expect(screen.getByText('Slice ID')).toBeInTheDocument();
  });
});
